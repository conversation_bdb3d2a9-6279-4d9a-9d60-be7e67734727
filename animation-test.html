<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>弹窗动画效果测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }

        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.2s ease;
        }

        .test-button:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        /* SelectionBar 样式 */
        .selection-bar-container {
            position: relative;
            display: inline-block;
            margin: 20px;
        }

        .selection-bar-demo {
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px) scale(0.95);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            
            position: absolute;
            display: flex;
            align-items: center;
            background-color: white;
            border-radius: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            padding: 8px 12px;
            border: 1px solid #e5e7eb;
            width: fit-content;
            top: 100%;
            left: 0;
            margin-top: 10px;
        }

        .selection-bar-demo.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0) scale(1);
        }

        .selection-bar-demo.hide {
            opacity: 0;
            visibility: hidden;
            transform: translateY(-5px) scale(0.98);
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Modal 样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.1);
            z-index: 10000;
            display: flex;
            align-items: flex-start;
            justify-content: center;
            padding: 20px 0;
            animation: overlayFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .modal-overlay.closing {
            animation: overlayFadeOut 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .modal-demo {
            position: relative;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            width: 500px;
            border: 1px solid #E5E7EB;
            padding: 16px;
            margin: 20px auto;
            animation: modalSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .modal-demo.closing {
            animation: modalSlideOut 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* 浮动按钮样式 */
        .floating-button-demo {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: #007bff;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 20px rgba(0, 123, 255, 0.3);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            animation: pulse 3s infinite;
            color: white;
            font-weight: bold;
        }

        .floating-button-demo:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
        }

        /* 动画定义 */
        @keyframes overlayFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes overlayFadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-12px) scale(0.96);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes modalSlideOut {
            from {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
            to {
                opacity: 0;
                transform: translateY(-8px) scale(0.98);
            }
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 4px 20px rgba(0, 123, 255, 0.3);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 6px 30px rgba(0, 123, 255, 0.5);
                transform: scale(1.02);
            }
            100% {
                box-shadow: 0 4px 20px rgba(0, 123, 255, 0.3);
                transform: scale(1);
            }
        }

        .demo-text {
            background: #f0f8ff;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            user-select: text;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>弹窗动画效果测试</h1>
        
        <div class="test-section">
            <h2>1. 划词工具栏动画测试</h2>
            <p>点击按钮测试划词工具栏的显示/隐藏动画效果：</p>
            <div class="selection-bar-container">
                <button class="test-button" onclick="toggleSelectionBar()">切换划词工具栏</button>
                <div id="selectionBarDemo" class="selection-bar-demo">
                    <span>🔍 总结</span>
                    <span style="margin: 0 8px;">|</span>
                    <span>🌐 翻译</span>
                    <span style="margin: 0 8px;">|</span>
                    <span>⋮</span>
                    <span style="margin: 0 8px;">|</span>
                    <span>×</span>
                </div>
            </div>
            <div class="demo-text">
                这是一段可以选中的示例文本，用于测试划词工具栏的效果。选中这段文字时应该会显示工具栏。
            </div>
        </div>

        <div class="test-section">
            <h2>2. AI处理弹窗动画测试</h2>
            <p>点击按钮测试AI处理弹窗的显示/隐藏动画效果：</p>
            <button class="test-button" onclick="showModal()">显示AI处理弹窗</button>
        </div>

        <div class="test-section">
            <h2>3. 浮动按钮动画测试</h2>
            <p>查看右下角的浮动按钮，它具有脉冲动画和悬停效果。</p>
        </div>

        <div class="test-section">
            <h2>动画特性说明</h2>
            <ul>
                <li><strong>淡入动画</strong>：所有弹窗显示时都有300-500毫秒的淡入效果</li>
                <li><strong>淡出动画</strong>：隐藏时有200-300毫秒的淡出效果</li>
                <li><strong>缩放效果</strong>：弹窗显示/隐藏时带有轻微的缩放变化</li>
                <li><strong>滑动效果</strong>：弹窗有向上滑动的进入效果</li>
                <li><strong>缓动函数</strong>：使用 cubic-bezier(0.4, 0, 0.2, 1) 实现自然的动画曲线</li>
                <li><strong>自动触发</strong>：动画在显示/隐藏状态切换时自动触发</li>
            </ul>
        </div>
    </div>

    <!-- 浮动按钮 -->
    <div class="floating-button-demo">AI</div>

    <script>
        let selectionBarVisible = false;
        let modalVisible = false;

        function toggleSelectionBar() {
            const demo = document.getElementById('selectionBarDemo');
            if (selectionBarVisible) {
                demo.classList.remove('show');
                demo.classList.add('hide');
                setTimeout(() => {
                    demo.classList.remove('hide');
                }, 200);
                selectionBarVisible = false;
            } else {
                demo.classList.add('show');
                selectionBarVisible = true;
            }
        }

        function showModal() {
            if (modalVisible) return;
            
            const overlay = document.createElement('div');
            overlay.className = 'modal-overlay';
            overlay.onclick = (e) => {
                if (e.target === overlay) {
                    hideModal(overlay);
                }
            };

            const modal = document.createElement('div');
            modal.className = 'modal-demo';
            modal.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <div style="background: #4096FF; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;">AI</div>
                        <span>文本处理</span>
                    </div>
                    <button onclick="hideModal(this.closest('.modal-overlay'))" style="background: none; border: none; font-size: 18px; cursor: pointer;">×</button>
                </div>
                <div style="padding: 20px; background: #f9fafb; border-radius: 6px; margin-bottom: 16px;">
                    <p style="margin: 0; color: #666;">正在处理您的文本...</p>
                </div>
                <div style="display: flex; gap: 8px; justify-content: flex-end;">
                    <button class="test-button" onclick="hideModal(this.closest('.modal-overlay'))">关闭</button>
                </div>
            `;

            overlay.appendChild(modal);
            document.body.appendChild(overlay);
            modalVisible = true;
        }

        function hideModal(overlay) {
            overlay.classList.add('closing');
            overlay.querySelector('.modal-demo').classList.add('closing');
            
            setTimeout(() => {
                document.body.removeChild(overlay);
                modalVisible = false;
            }, 200);
        }

        // 文本选择事件
        document.addEventListener('mouseup', function() {
            const selection = window.getSelection();
            if (selection.toString().trim() && !selectionBarVisible) {
                setTimeout(() => toggleSelectionBar(), 100);
            }
        });
    </script>
</body>
</html>
